{% extends 'billing/base.html' %}

{% block title %}Customers - Billing System{% endblock %}
{% block page_title %}Customers{% endblock %}

{% block page_actions %}
<a href="/admin/billing/customer/add/" class="btn btn-primary">
    <i class="fas fa-plus"></i> Add Customer
</a>
{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Customer List</h6>
    </div>
    <div class="card-body">
        {% if customers %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                <a href="{% url 'customer_detail' customer.id %}">{{ customer.name }}</a>
                            </td>
                            <td>{{ customer.email }}</td>
                            <td>{{ customer.phone|default:"-" }}</td>
                            <td>{{ customer.created_at|date:"M d, Y" }}</td>
                            <td>
                                <a href="{% url 'customer_detail' customer.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="/admin/billing/customer/{{ customer.id }}/change/" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No customers found</h5>
                <p class="text-muted">Get started by adding your first customer.</p>
                <a href="/admin/billing/customer/add/" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Customer
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
