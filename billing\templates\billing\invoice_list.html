{% extends 'billing/base.html' %}

{% block title %}Invoices - Billing System{% endblock %}
{% block page_title %}Invoices{% endblock %}

{% block page_actions %}
<a href="{% url 'create_invoice' %}" class="btn btn-primary">
    <i class="fas fa-plus"></i> Create Invoice
</a>
{% endblock %}

{% block content %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Invoice List</h6>
    </div>
    <div class="card-body">
        {% if invoices %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Customer</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>
                                <a href="{% url 'invoice_detail' invoice.id %}">{{ invoice.invoice_number }}</a>
                            </td>
                            <td>
                                <a href="{% url 'customer_detail' invoice.customer.id %}">{{ invoice.customer.name }}</a>
                            </td>
                            <td>{{ invoice.issue_date }}</td>
                            <td>{{ invoice.due_date }}</td>
                            <td>
                                <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'sent' %}info{% elif invoice.status == 'overdue' %}danger{% else %}secondary{% endif %} status-badge">
                                    {{ invoice.get_status_display }}
                                </span>
                            </td>
                            <td>${{ invoice.total_amount|floatformat:2 }}</td>
                            <td>
                                <a href="{% url 'invoice_detail' invoice.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="/admin/billing/invoice/{{ invoice.id }}/change/" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No invoices found</h5>
                <p class="text-muted">Get started by creating your first invoice.</p>
                <a href="{% url 'create_invoice' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Invoice
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
