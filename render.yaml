databases:
  - name: billing-system-db
    databaseName: billing_system
    user: billing_user

services:
  - type: web
    name: billing-system
    env: python
    buildCommand: "./build.sh"
    startCommand: "gunicorn billing_system.wsgi:application"
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: billing-system-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: False
