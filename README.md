# Django Billing System

A simple billing system built with Django that allows you to manage customers, create invoices, and track payments.

## Features

- **Customer Management**: Add, view, and manage customer information
- **Invoice Creation**: Create detailed invoices with multiple line items
- **Payment Tracking**: Record and track payments against invoices
- **Dashboard**: Overview of key metrics and recent activity
- **Admin Interface**: Full Django admin interface for data management

## Models

- **Customer**: Store customer information (name, email, phone, address)
- **Invoice**: Create invoices with status tracking (draft, sent, paid, overdue, cancelled)
- **InvoiceItem**: Line items for invoices with quantity and pricing
- **Payment**: Track payments against invoices with multiple payment methods

## Local Development

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate virtual environment: `source venv/Scripts/activate` (Windows) or `source venv/bin/activate` (Linux/Mac)
4. Install dependencies: `pip install -r requirements.txt`
5. Run migrations: `python manage.py migrate`
6. Create superuser: `python manage.py createsuperuser`
7. Run development server: `python manage.py runserver`

## Admin Access

- Username: admin
- Password: admin123
- URL: http://localhost:8000/admin/

## Deployment to Render

This application is configured for easy deployment to Render.com:

1. Push your code to a GitHub repository
2. Connect your GitHub repository to Render
3. Render will automatically detect the `render.yaml` file and set up:
   - A PostgreSQL database
   - A web service running the Django application
   - Environment variables for production

The application will be automatically deployed with:
- PostgreSQL database
- Static file serving via WhiteNoise
- Production-ready settings
- Automatic migrations on deployment

## Environment Variables

For production deployment, the following environment variables are used:
- `DATABASE_URL`: PostgreSQL connection string (automatically set by Render)
- `SECRET_KEY`: Django secret key (automatically generated by Render)
- `DEBUG`: Set to False for production

## Usage

1. **Add Customers**: Use the admin interface or the customers page to add customer information
2. **Create Invoices**: Use the "Create Invoice" page to generate new invoices with line items
3. **Track Payments**: Record payments against invoices through the admin interface
4. **Monitor Dashboard**: View key metrics and recent activity on the dashboard

## Technology Stack

- **Backend**: Django 5.2.6
- **Database**: SQLite (development) / PostgreSQL (production)
- **Frontend**: Bootstrap 5.1.3 with Font Awesome icons
- **Deployment**: Render.com with Gunicorn and WhiteNoise
# billing
