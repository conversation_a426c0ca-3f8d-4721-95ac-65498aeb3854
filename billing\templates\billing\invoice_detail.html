{% extends 'billing/base.html' %}

{% block title %}Invoice {{ invoice.invoice_number }} - Billing System{% endblock %}
{% block page_title %}Invoice {{ invoice.invoice_number }}{% endblock %}

{% block page_actions %}
<a href="{% url 'invoice_list' %}" class="btn btn-secondary">
    <i class="fas fa-arrow-left"></i> Back to Invoices
</a>
<a href="/admin/billing/invoice/{{ invoice.id }}/change/" class="btn btn-primary">
    <i class="fas fa-edit"></i> Edit Invoice
</a>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Invoice Details -->
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Details</h6>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Bill To:</h5>
                        <strong>{{ invoice.customer.name }}</strong><br>
                        {{ invoice.customer.email }}<br>
                        {% if invoice.customer.phone %}{{ invoice.customer.phone }}<br>{% endif %}
                        {% if invoice.customer.address %}{{ invoice.customer.address|linebreaks }}{% endif %}
                    </div>
                    <div class="col-md-6 text-end">
                        <h5>Invoice Information:</h5>
                        <strong>Invoice #:</strong> {{ invoice.invoice_number }}<br>
                        <strong>Issue Date:</strong> {{ invoice.issue_date }}<br>
                        <strong>Due Date:</strong> {{ invoice.due_date }}<br>
                        <strong>Status:</strong> 
                        <span class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'sent' %}info{% elif invoice.status == 'overdue' %}danger{% else %}secondary{% endif %} status-badge">
                            {{ invoice.get_status_display }}
                        </span>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td>{{ item.description }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>${{ item.unit_price|floatformat:2 }}</td>
                                <td>${{ item.total_price|floatformat:2 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="3" class="text-end">Subtotal:</th>
                                <th>${{ invoice.subtotal|floatformat:2 }}</th>
                            </tr>
                            {% if invoice.tax_rate > 0 %}
                            <tr>
                                <th colspan="3" class="text-end">Tax ({{ invoice.tax_rate }}%):</th>
                                <th>${{ invoice.tax_amount|floatformat:2 }}</th>
                            </tr>
                            {% endif %}
                            <tr class="table-primary">
                                <th colspan="3" class="text-end">Total:</th>
                                <th>${{ invoice.total_amount|floatformat:2 }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                {% if invoice.notes %}
                <div class="mt-4">
                    <h6>Notes:</h6>
                    <p>{{ invoice.notes|linebreaks }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Payment Information -->
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Summary</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Total Amount:</strong> ${{ invoice.total_amount|floatformat:2 }}<br>
                    <strong>Total Paid:</strong> ${{ total_paid|floatformat:2 }}<br>
                    <strong>Balance Due:</strong> 
                    <span class="{% if balance_due > 0 %}text-danger{% else %}text-success{% endif %}">
                        ${{ balance_due|floatformat:2 }}
                    </span>
                </div>
                
                {% if balance_due > 0 %}
                <a href="/admin/billing/payment/add/?invoice={{ invoice.id }}" class="btn btn-success btn-sm w-100">
                    <i class="fas fa-plus"></i> Record Payment
                </a>
                {% endif %}
            </div>
        </div>

        <!-- Payment History -->
        {% if payments %}
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment History</h6>
            </div>
            <div class="card-body">
                {% for payment in payments %}
                <div class="mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
                    <strong>${{ payment.amount|floatformat:2 }}</strong><br>
                    <small class="text-muted">
                        {{ payment.payment_date }} - {{ payment.get_payment_method_display }}
                        {% if payment.reference_number %}
                            <br>Ref: {{ payment.reference_number }}
                        {% endif %}
                    </small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
